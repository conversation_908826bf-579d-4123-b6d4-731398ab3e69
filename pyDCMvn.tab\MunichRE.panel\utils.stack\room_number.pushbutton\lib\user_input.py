import clr
from collections import namedtuple
from pyrevit import DB
from DCMvn.forms.wpfforms import SelectFromList
from DCMvn.forms import alert
from DCMvn.core.framework import System, List

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)


ParamDef = namedtuple('ParamDef', ['name', 'is_builtin', 'definition', 'built_in_parameter'])


def select_link(document):
    """
    Selects the first link in the document.
    """
    collector = (DB.FilteredElementCollector(document)
                 .OfClass(DB.RevitLinkInstance)
                 .Where(lambda x: DB.RevitLinkType.IsLoaded(document, x.GetTypeId()))
                 .ToList())
    
    if collector.Count == 0:
        alert("No linked models found in the document.", exitscript=True)
    
    selected_link = SelectFromList.show(context=collector, 
                                              title="Select a Linked Model",
                                              multiselect=False,
                                              name_attr="Name",
                                              width=450,
                                              height=400)
    if not selected_link:
        alert("No linked model selected.", exitscript=True)
    
    return selected_link


def get_parameter(param_def, element):
    # type: (ParamDef, DB.Element) -> DB.Parameter
    """
    Get the parameter from the element based on the parameter definition.
    
    Args:
        param_def (ParamDef): The parameter definition.
        element (DB.Element): The Revit element.
    
    Returns:
        DB.Parameter: The parameter if found, otherwise None.
    """
    if param_def.is_builtin:
        return element.get_Parameter(param_def.built_in_parameter)
    elif param_def.definition is not None:
        return element.get_Parameter(param_def.definition)
    elif param_def.name is not None:
        return element.LookupParameter(param_def.name)
    
    return None


def is_parameter_applicable(param_def, element):
    # type: (ParamDef, DB.Element) -> bool
    
    para = get_parameter(param_def, element)
    return para is not None and not para.IsReadOnly
    

def select_parameter(elements, message="Select a parameter"):
    # type (list[DB.Element]) -> list[ParamDef]
    """
    Selects the 'Room Number' parameter from the linked model.
    """

    if not elements:
        alert("No elements found in the linked model.", exitscript=True)

    doc = elements.FirstOrDefault().Document  # type: DB.Document
    categories = List[DB.ElementId](elements.Where(lambda e: e.Category is not None)
                        .Select(lambda e: e.Category.Id)
                        .Distinct()
                        .ToList())
    
    parameterIds = DB.ParameterFilterUtilities.GetFilterableParametersInCommon(elements.FirstOrDefault().Document, categories)
    
    param_defs = []
    for id in parameterIds:
        if id.Value < 0:
            param = DB.BuiltInParameter(id.Value)
            param_defs.append(ParamDef(name=DB.LabelUtils.GetLabelFor(param),
                                       is_builtin=True, 
                                       definition=None, 
                                       built_in_parameter=param))
        else:
            param = doc.GetElement(id)
            if param is not None and isinstance(param, DB.ParameterElement):
                definition = param.GetDefinition()
                param_defs.append(ParamDef(name=param.Name,
                                           is_builtin=False,
                                           definition=definition, 
                                           built_in_parameter=None))
    
    valid_params = param_defs.Where(lambda p: elements.Any(lambda e: is_parameter_applicable(p, e))).ToList()
    selected_param = SelectFromList.show(context=valid_params,
                                        title=message,
                                        multiselect=False,
                                        name_attr="name",
                                        width=450,
                                        height=400)
    if not selected_param:
        alert("No parameter selected.", exitscript=True)
        
    return selected_param


def is_physical_category(category):
    # type: (DB.Category) -> bool

    if category is None:
        return False
    
    builtinCategory = DB.BuiltInCategory(category.Id.Value)
    if (builtinCategory == DB.BuiltInCategory.OST_HVAC_Zones
        or builtinCategory == DB.BuiltInCategory.OST_Lines
        or builtinCategory.ToString().Contains("System")):
        return False

    return category.CategoryType == DB.CategoryType.Model and category.CanAddSubcategory


def get_element_physical_filter(document):
    """
    Returns an ElementMulticategoryFilter for physical model categories, 
    excluding HVAC Zones, Lines, and 'System' categories.
    """
    categories = document.Settings.Categories  # DB.CategorySet
    hvac_zone_id = DB.ElementId(DB.BuiltInCategory.OST_HVAC_Zones)
    line_id = DB.ElementId(DB.BuiltInCategory.OST_Lines)

    model_category_ids = List[DB.ElementId](
        [cat.Id for cat in categories
         if cat.CategoryType == DB.CategoryType.Model
         and cat.CanAddSubcategory
         and cat.Id != hvac_zone_id
         and cat.Id != line_id
         and not DB.BuiltInCategory(cat.Id.Value).ToString().Contains("System")]
    )

    return DB.ElementMulticategoryFilter(model_category_ids)


def select_categories(document):
    """
    Selects categories from the linked model.
    """
    categories = document.Settings.Categories # type: DB.CategorySet
    categories = [cat for cat in categories if is_physical_category(cat)]
    return SelectFromList.show(context=categories,
                                        title="Select MEP Categories",
                                        multiselect=True,
                                        name_attr="Name",
                                        width=450,
                                        height=400)