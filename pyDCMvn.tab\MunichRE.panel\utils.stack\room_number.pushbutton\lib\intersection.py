import clr
import System
from pyrevit import DB, HOST_APP
from DCMvn.revit.geometry import are_elements_intersect
from DCMvn.revit.query import DQuery
from DCMvn.core.framework import List
from user_input import get_parameter
from space_props import GenericSpaceProperties

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

class IntersectionChecker:
    """
    A class to check if two sets of elements intersect and track comprehensive statistics.
    """

    def __init__(self, rooms, mep_elements):
        """ Initialize the IntersectionChecker with two sets of elements.

        Args:
            rooms (list): A list of room elements.
            mep_elements (list): A list of MEP elements.
        """
        self.rooms = rooms
        self.mep_elements = mep_elements
        self.total_rooms = len(rooms)
        self.total_mep_elements = len(mep_elements)
        self.match_set = []

        # Statistics tracking
        self.successful_mappings = 0
        self.empty_rooms = 0
        self.rooms_with_mep = 0
        self.unmapped_mep_elements = []
        self.rooms_processed = 0

    def check_intersection(self, transform):
        # type: (DB.Transform) -> None
        """ Check for intersections between rooms and MEP elements and track statistics.

        Args:
            transform: The transformation to apply to room bounding boxes.
        """

        mep_element_ids = List[DB.ElementId]([i.Id for i in self.mep_elements])
        mapped_mep_ids = set()

        for room in self.rooms:
            self.rooms_processed += 1
            room_has_mep = False

            try:
                origin_bbox = room.get_BoundingBox(None)
                if origin_bbox is None:
                    continue

                min_point = transform.OfPoint(origin_bbox.Min)
                max_point = transform.OfPoint(origin_bbox.Max)

                outline = DB.Outline(min_point, max_point)
                bbox_filter = DB.BoundingBoxIntersectsFilter(outline)  # type: DB.BoundingBoxIntersectsFilter

                mep_elements_in_bbox = DB.FilteredElementCollector(HOST_APP.doc, mep_element_ids) \
                    .WhereElementIsNotElementType() \
                    .WherePasses(bbox_filter) \
                    .ToElements()

                for mep_element in mep_elements_in_bbox:
                    try:
                        if are_elements_intersect(room, mep_element, largest_solid_only=True):
                            self.match_set.append((room, mep_element))
                            mapped_mep_ids.add(mep_element.Id.IntegerValue)
                            room_has_mep = True
                    except Exception:
                        # Silent error handling - don't print exceptions to output
                        pass

                # Track room statistics
                if room_has_mep:
                    self.rooms_with_mep += 1
                else:
                    self.empty_rooms += 1

            except Exception:
                # Silent error handling for room processing
                pass

        # Track unmapped MEP elements
        for mep_element in self.mep_elements:
            if mep_element.Id.IntegerValue not in mapped_mep_ids:
                self.unmapped_mep_elements.append(mep_element)
                
    def check_intersection_round2(self):
        accept_range = DB.UnitUtils.ConvertToInternalUnits(100, DB.UnitTypeId.Millimeters)
        ray_cast_direction_list = [DB.XYZ(1, 0, 0), DB.XYZ(-1, 0, 0), DB.XYZ(0, 1, 0), DB.XYZ(0, -1, 0), DB.XYZ(0, 0, 1), DB.XYZ(0, 0, -1)]
        
        element_filter = DQuery.create_parameter_string_filter(GenericSpaceProperties.ExportToIfcAs, GenericSpaceProperties.IfcSpaceType, DB.FilterStringContains())
        intersector = DB.ReferenceIntersector(element_filter, DB.FindReferenceTarget.Element, HOST_APP.doc.ActiveView)
        
        for room in self.self.rooms:
            for mep_element in self.unmapped_mep_elements:
                try:
                    for ray_cast_direction in ray_cast_direction_list:
                        reference_context = intersector.FindNearest(mep_element.Location.Point, ray_cast_direction)
                        if reference_context:
                            if reference_context.Proximity < accept_range:
                                self.match_set.append((room, mep_element))
                                self.unmapped_mep_elements.remove(mep_element)
                                break
                except Exception:
                    pass
                
                
                
    def set_parameters(self, source_param, target_param):
        """Set the source and target parameters for the intersection check and track success/failure.

        Args:
            source_param (ParamDef): The parameter definition for the source.
            target_param (ParamDef): The parameter definition for the target.
        """

        for room, mep_element in self.match_set:
            try:
                if source_param and target_param:
                    source = get_parameter(source_param, room)
                    target = get_parameter(target_param, mep_element)

                    if source and target and not target.IsReadOnly:
                        target.Set(source.AsString())
                        self.successful_mappings += 1
            except Exception:
                pass

    def get_statistics(self):
        """Get comprehensive statistics about the intersection checking process.

        Returns:
            dict: Dictionary containing all statistics
        """
        return {
            'total_rooms': self.total_rooms,
            'total_mep_elements': self.total_mep_elements,
            'rooms_processed': self.rooms_processed,
            'successful_mappings': self.successful_mappings,
            'empty_rooms': self.empty_rooms,
            'rooms_with_mep': self.rooms_with_mep,
            'unmapped_mep_elements': self.unmapped_mep_elements,
            'total_intersections_found': len(self.match_set)
        }