# coding: utf-8
from DCMvn.core import DB

class GenericSpaceProperties:
    Guid = DB.BuiltInParameter.IFC_GUID
    ExportToIfcAs = DB.BuiltInParameter.IFC_EXPORT_ELEMENT_AS
    IfcSpaceType = "IfcSpaceType"
    Name = "AW_ARC.Raumname"
    Number = "AW_ARC.Raumnummer ARC"
    Level = "AW_ARC.Geschossnummer"


class RoomNumberToolConstants:
    """Constants for the Room Number Assignment Tool HTML output and messages."""

    # Tool header and main title
    TOOL_TITLE = '<h1 style="color:blue; text-align:center;"> :house: Room Number Assignment Tool</h1>'
    TOOL_DESCRIPTION = '<div style="text-align:center; margin: 10px 0; padding: 10px; background-color: #e3f2fd; border-radius: 5px;"><strong>Mapping MEP elements to room properties...</strong></div>'

    # Section headers with emoji icons
    SECTION_HEADERS = {
        'DATA_COLLECTION': '<h3 style="color:blue;"> :bar_chart: Initializing Data Collection</h3>',
        'INTERSECTION_CHECK': '<h3 style="color:blue;"> :magnifying_glass_tilted_left: Checking Intersections</h3>',
        'PARAMETER_SETTING': '<h3 style="color:blue;"> :gear: Setting Parameters</h3>',
        'OPERATION_SUMMARY': '<h2 style="color:blue; border-bottom: 2px solid #007acc; padding-bottom: 5px;"> :clipboard: OPERATION SUMMARY</h2>'
    }

    # Status messages
    STATUS_MESSAGES = {
        'LOADING_DATA': 'Loading rooms and MEP elements...',
        'ANALYZING_RELATIONSHIPS': 'Analyzing spatial relationships between rooms and MEP elements...',
        'ASSIGNING_PARAMETERS': 'Assigning room numbers to MEP elements...',
        'NO_INTERSECTIONS': '<strong style="color:orange;"> :warning: No intersections found between rooms and MEP elements.</strong>',
        'INTERSECTIONS_FOUND': '<strong style="color:green;">✓ Found {} intersections</strong>',
        'SUCCESSFUL_MAPPING': '<strong style="color:green;">✓ Successfully mapped {} MEP elements to rooms</strong>',
        'FAILED_MAPPING': '<strong style="color:orange;"> :warning: Failed to map {} MEP elements</strong>',
        'DATA_LOADED': '<strong style="color:green;">✓ Loaded {} rooms and {} MEP elements</strong>'
    }

    # HTML styling patterns
    HTML_STYLES = {
        'PARAMETER_INFO_BOX': '<div style="background-color: #e3f2fd; padding: 10px; border-radius: 5px; margin: 10px 0;">',
        'SUMMARY_TABLE_CONTAINER': '<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">',
        'SUMMARY_TABLE': '<table style="width: 100%; border-collapse: collapse;">',
        'UNMAPPED_ELEMENTS_BOX': '<div style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0;">',
        'SUCCESS_FINAL_BOX': '<div style="text-align:center; margin: 20px 0; padding: 15px; background-color: #e8f5e8; border-radius: 5px; border: 2px solid #28a745;">',
        'ERROR_FINAL_BOX': '<div style="text-align:center; margin: 20px 0; padding: 15px; background-color: #f8d7da; border-radius: 5px; border: 2px solid #dc3545;">',
        'CLOSE_DIV': '</div>'
    }

    # Table row templates
    TABLE_ROWS = {
        'TOTAL_ROOMS': '<tr><td style="padding: 5px; font-weight: bold;">Total rooms processed:</td><td style="padding: 5px;"><strong style="color: blue;">{}</strong></td></tr>',
        'TOTAL_MEP': '<tr><td style="padding: 5px; font-weight: bold;">Total MEP elements found:</td><td style="padding: 5px;"><strong style="color: blue;">{}</strong></td></tr>',
        'SUCCESSFUL_MAPPINGS': '<tr><td style="padding: 5px; font-weight: bold;">MEP elements successfully mapped:</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>',
        'FAILED_MAPPINGS': '<tr><td style="padding: 5px; font-weight: bold;">Failed MEP element mappings:</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>',
        'EMPTY_ROOMS': '<tr><td style="padding: 5px; font-weight: bold;">Empty rooms (no MEP elements):</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>',
        'ROOMS_WITH_MEP': '<tr><td style="padding: 5px; font-weight: bold;">Rooms containing MEP elements:</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>'
    }

    # Parameter mapping message templates
    PARAMETER_MAPPING_MESSAGES = {
        'SOURCE_PARAMETER': 'Source parameter (from rooms): <strong>{}</strong>',
        'TARGET_PARAMETER': 'Target parameter (for MEP elements): <strong>{}</strong>'
    }

    # MEP categories display
    MEP_CATEGORIES = {
        'HEADER': 'Selected MEP categories:',
        'BULLET_POINT': '• {}'
    }

    # Unmapped elements section
    UNMAPPED_ELEMENTS = {
        'HEADER': '<h4 style="color: #856404; margin-top: 0;"> :warning: Unmapped MEP Elements</h4>',
        'DESCRIPTION': '<p style="margin-bottom: 10px;">The following MEP elements could not be mapped to room properties:</p>',
        'BULLET_POINT': '• {}'
    }

    # Final status messages
    FINAL_STATUS = {
        'SUCCESS_TITLE': '<h2 style="color:green; margin: 0;"> :party_popper: Room Number Assignment Completed Successfully!</h2>',
        'SUCCESS_DESCRIPTION': '<p style="margin: 5px 0;">Successfully mapped {} MEP elements to rooms.</p>',
        'ERROR_TITLE': '<h2 style="color:red; margin: 0;"> :warning: No Room Numbers Assigned</h2>',
        'ERROR_DESCRIPTION': '<p style="margin: 5px 0;">No MEP elements were successfully mapped to rooms. Check the output above for details.</p>'
    }